import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.securityVulServer}`;
const basePath03 = `${ServerNames.securityVulServer}/sum`;
const basePath02 = `${ServerNames.securityVulServer}/vul`;
const basePath04 = `${ServerNames.eamPmServer}`;
const basePath05 = `/security-collect`;
//

const pathList = {
    vulDetailByAssetUrl: "/vulDetails", // 资产视角表格数据
    vulDetailByVulUrl: "/v3/sum/vulDetails", // 3.0资产视角表格数据
    importVulDetailByAssetUrl: "/v3/suba/import/vulDetailByAsset", // 资产视角表格数据导出
    templateTypesUrl: "/excel/templateTypes", // 资产视角ip详情导入种类
};

const vulDetailByAssetMethod = (params: any) => {
    return http.postJson(`${basePath}${pathList.vulDetailByVulUrl}`, params);
}

const importVulDetailByAssetMethod = (params: any) => {
    return http.postBlob(`${basePath}${pathList.importVulDetailByAssetUrl}`, params);
}

const templateTypesMethod = () => {
    return http.get(`${basePath02}${pathList.templateTypesUrl}`);
}

const getAllIp = (params: any) =>
    http.postJson<any>(
        `${basePath}/v3/sum/vulAssetIps`,
        params
    );

const bulkOrder = (params: any) =>
    http.postJson<any>(
        `${basePath04}/sheets/process/form/securityVulDeal/data/info/start`,
        params
    );

const batchVerification = (params: any) =>
    http.postJson<any>(
        `${basePath05}/v3/vul/addNewTask`,
        params
    );

const removeToUndisposedBatch = (params: any) =>
    http.postJson<any>(
        `${basePath}/v3/sum/removeToUndisposedBatch`,
        params
    );

export {
    vulDetailByAssetMethod,
    importVulDetailByAssetMethod,
    templateTypesMethod,
    getAllIp,
    bulkOrder,
    batchVerification,
    removeToUndisposedBatch
};
